import os.path
import time

from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import Q<PERSON>ainter, QColor, QFont
from PySide6.QtWidgets import QFrame, QSizePolicy, QLabel, QVBoxLayout, QPushButton, QWidget, QHBoxLayout, QBoxLayout

from .gl_widget.urdf_viewer import URDFViewer
from ..components.base_widgets import BorderlessLayout
from ..ui.urdl_panel import Ui_Form
from ... import APP_ROOT_PATH
from ...utils.resource_helper import get_image


class StatusButton(QPushButton):
    def __init__(self, text, parent=None):
        super().__init__(text, parent)

        self.dot_color = QColor("#2DC96E")
        self.setFixedSize(110, 40)

        self.setStyleSheet("""
            QPushButton {
                background-color: #35363A;
                color: #FFFFFF;
                border: none;
                border-radius: 8px;
                font-family: 'Alibaba PuHuiTi 2.0';
                font-size: 16px;
                font-weight: 300;
                letter-spacing: 0px;  /* 可能不支持，依赖平台 */
            }
        """)

    def paintEvent(self, event):
        super().paintEvent(event)

        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # Get the tab rect
        rect = self.rect()

        # Set the dot color
        painter.setBrush(self.dot_color)
        painter.setPen(Qt.NoPen)

        # Draw the dot
        dot_size = 12
        dot_x = rect.left() + 5
        dot_y = rect.center().y() - dot_size // 2
        painter.drawEllipse(dot_x, dot_y, dot_size, dot_size)


class URDFWidget(QFrame, Ui_Form):
    switch_hand_type_signal = Signal(str)

    urdf_loading_finished = Signal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setupUi(self)

        self._hand_version_buttons = []
        self.urdf_viewer = None  # 延时初始化

        self._init_ui()

        self.init_urdf_viewer()

    def _init_ui(self):
        self.tabWidget_model.tabBar().hide()

        # 设置主界面布局
        self.layout().setContentsMargins(20, 40, 20, 20)
        self.setStyleSheet("background-color: #2E3034; border: 1px; border-radius: 5px;")

        self.label_sh.setFixedSize(40, 40)
        self.label_sh.setStyleSheet("border-radius: 8px; background-color: transparent;")
        pix = get_image("vector.svg")
        pix = pix.scaled(28, 28, Qt.AspectRatioMode.KeepAspectRatio)
        self.label_sh.setPixmap(pix)

        # urdf - 设置布局但不立即初始化viewer
        self.frame.setLayout(QVBoxLayout())
        self.frame.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Expanding)

        # 状态设置
        self.horizontalLayout_head.setContentsMargins(20, 0, 20, 0)
        self.horizontalLayout_head.setSpacing(0)
        self.status_button = StatusButton(self.tr("Connected"), self)
        self.horizontalLayout_head.insertWidget(-1, self.status_button)

        # 名称标签
        self.label_name.setFixedHeight(40)
        self.label_name.setStyleSheet("""
            QLabel {
                background-color: transparent;
                color: white;
                border: none;
                font-family: 'Alibaba PuHuiTi 2.0';
                font-size: 24px;
                font-weight: 300;
                letter-spacing: 0px;  /* 可能不支持，依赖平台 */
            }
        """)

        # tip
        self.label_tip.setStyleSheet("""
            QLabel {
                background-color: transparent;
                color: gray;
                border: none;
                font-family: 'Alibaba PuHuiTi 2.0';
                font-size: 16px;
                font-weight: 300;
                letter-spacing: 0px;  /* 可能不支持，依赖平台 */
            }
        """)

        # 切换
        self.widget_hand_type = QWidget(self)
        self.widget_hand_type.setLayout(BorderlessLayout(QBoxLayout.Direction.LeftToRight))

        self.widget_hand_type.setFixedSize(332, 48)
        self.widget_hand_type.setStyleSheet("border: none; border-radius: 8px; padding: 4 4 4 4;background-color: #444345;")
        self.widget_hand_type.setLayout(QHBoxLayout())
        self.widget_hand_type.layout().setSpacing(0)
        self.widget_hand_type.layout().setContentsMargins(4, 0, 4, 0)
        for hand_type in ["Left", "Right", "Both"]:
            button = QPushButton(hand_type)
            button.setFixedHeight(40)
            button.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Preferred)
            button.setCheckable(True)
            button.setStyleSheet("""
            QPushButton {
                font-family: 'Alibaba PuHuiTi 2.0';
                font-weight: 300;
                font-size: 20px;
                background-color: #3F4749;
                color: white;
                border: none;
            }
            QPushButton:checked {
                border: none solid white;
                background-color: #52595A;
            }
            """)
            self._hand_version_buttons.append(button)
            self.widget_hand_type.layout().addWidget(button)
            button.clicked.connect(self._on_hand_type_button_clicked)

        self.verticalLayout_switch.addWidget(self.widget_hand_type)

        # 连接按钮
        self.pushButton_connect.setFixedSize(200, 58)
        self.pushButton_connect.setText("Connect")
        self.pushButton_connect.setStyleSheet(f"""
            font-family: 'Alibaba PuHuiTi 2.0';
            font-weight: 300;
            font-size: 20px;
            background-color: #FF9429;
            color: black;
            border: none;
            border-radius: 8px;
            """)

    def _on_hand_type_button_clicked(self):
        self.switch_hand_type_signal.emit(self.sender().text())

    def switch_hand_type_button(self, hand_type_str: str):
        for button in self._hand_version_buttons:
            if button.text().lower() == hand_type_str.lower():
                button.setChecked(True)
            else:
                button.setChecked(False)

    def current_hand_type(self):
        for button in self._hand_version_buttons:
            if button.isChecked():
                return button.text()

        # 一个都没选择， 则默认选择所有
        return "Both"

    def set_both_button_visible(self, visible: bool):
        for button in self._hand_version_buttons:
            if button.text() == "Both":
                button.setVisible(visible)

    def urdf_loading_finished(self):
        self.urdf_viewer.set_camera_parameters(distance=0.18,
                                               azimuth=0,
                                               elevation=0,
                                               center_x=0,
                                               center_y=0,
                                               center_z=0.12)

        self.urdf_viewer.current_material_type = "Robot"
        self.urdf_viewer.update_material_properties()

    def init_urdf_viewer(self):
        if self.urdf_viewer is None:
            self.urdf_viewer = URDFViewer()
            self.urdf_viewer.loading_finished.connect(self.urdf_loading_finished)
            self.urdf_viewer.setFixedHeight(600)
            self.urdf_viewer.setSizePolicy(QSizePolicy.Policy.Preferred, QSizePolicy.Policy.Expanding)
            self.frame.layout().addWidget(self.urdf_viewer)
            self.urdf_viewer.load_urdf(os.path.join(APP_ROOT_PATH, "brainco-righthand-URDF-V2", "urdf", "brainco-righthand-URDF-V2.urdf"))
